package com.wzj.fragment_random_length;

import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.wzj.fragment_random_length.mvp.utils.SharedPreferencesUtil;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {

    private ViewPager viewPager;
    private MyPagerAdapter pagerAdapter;

    //用

    private TabLayout tabLayout;
    private List<Integer> fragmentIndexes;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        SharedPreferencesUtil.getInstance(this,"data");

        // 初始化 TabLayout 和 ViewPager
        tabLayout = findViewById(R.id.tabLayout);
        viewPager = findViewById(R.id.viewPager);

        // 初始化数据源
        fragmentIndexes = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            fragmentIndexes.add(i);
            SharedPreferencesUtil.putData("key"+i, false);
        }

        // 初始化适配器并设置给 ViewPager
        pagerAdapter = new MyPagerAdapter(getSupportFragmentManager());
        pagerAdapter.setFragmentIndexes(fragmentIndexes);
        viewPager.setAdapter(pagerAdapter);

        // 关联 TabLayout 和 ViewPager
        tabLayout.setupWithViewPager(viewPager);

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
//                viewPager.setCurrentItem(tab.getPosition(), false);
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
    }

    // 动态更新 Fragment 列表的数量
    private void updateFragments(int newCount) {
        fragmentIndexes.clear();
        for (int i = 0; i < newCount; i++) {
            fragmentIndexes.add(i);
        }
        pagerAdapter.setFragmentIndexes(fragmentIndexes);  // 更新适配器的数据
    }
}
