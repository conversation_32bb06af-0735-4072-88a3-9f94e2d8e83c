package com.wzj.fragment_random_length;

import android.os.Bundle;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.wzj.fragment_random_length.mvp.utils.MyPagerAdapter;
import com.wzj.fragment_random_length.mvp.utils.SharedPreferencesUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 主Activity
 * 优化了错误处理和生命周期管理
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int DEFAULT_FRAGMENT_COUNT = 6;

    private ViewPager viewPager;
    private MyPagerAdapter pagerAdapter;
    private TabLayout tabLayout;
    private List<Integer> fragmentIndexes;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            setContentView(R.layout.activity_main);
            initSharedPreferences();
            initViews();
            initData();
            setupViewPager();
            setupTabLayout();
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate: " + e.getMessage(), e);
            // 可以在这里添加错误恢复逻辑或显示错误提示
        }
    }

    /**
     * 初始化SharedPreferences
     */
    private void initSharedPreferences() {
        try {
            SharedPreferencesUtil.getInstance(this, "data");
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize SharedPreferences: " + e.getMessage(), e);
        }
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        tabLayout = findViewById(R.id.tabLayout);
        viewPager = findViewById(R.id.viewPager);

        if (tabLayout == null || viewPager == null) {
            throw new IllegalStateException("Required views not found in layout");
        }
    }

    /**
     * 初始化数据
     */
    private void initData() {
        fragmentIndexes = new ArrayList<>();
        for (int i = 0; i < DEFAULT_FRAGMENT_COUNT; i++) {
            fragmentIndexes.add(i);
            try {
                SharedPreferencesUtil.putData("key" + i, false);
            } catch (Exception e) {
                Log.w(TAG, "Failed to save data for key" + i + ": " + e.getMessage());
            }
        }
    }

    /**
     * 设置ViewPager
     */
    private void setupViewPager() {
        try {
            pagerAdapter = new MyPagerAdapter(getSupportFragmentManager());
            pagerAdapter.setFragmentIndexes(fragmentIndexes);
            viewPager.setAdapter(pagerAdapter);
        } catch (Exception e) {
            Log.e(TAG, "Failed to setup ViewPager: " + e.getMessage(), e);
        }
    }

    /**
     * 设置TabLayout
     */
    private void setupTabLayout() {
        try {
            // 关联 TabLayout 和 ViewPager
            tabLayout.setupWithViewPager(viewPager);

            tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
                @Override
                public void onTabSelected(TabLayout.Tab tab) {
                    // 可以在这里添加tab选中的逻辑
                    Log.d(TAG, "Tab selected: " + tab.getPosition());
                }

                @Override
                public void onTabUnselected(TabLayout.Tab tab) {
                    // 可以在这里添加tab取消选中的逻辑
                }

                @Override
                public void onTabReselected(TabLayout.Tab tab) {
                    // 可以在这里添加tab重新选中的逻辑
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Failed to setup TabLayout: " + e.getMessage(), e);
        }
    }

    /**
     * 动态更新Fragment列表的数量
     *
     * @param newCount 新的Fragment数量
     */
    public void updateFragments(int newCount) {
        if (newCount < 0) {
            Log.w(TAG, "Invalid fragment count: " + newCount);
            return;
        }

        try {
            fragmentIndexes.clear();
            for (int i = 0; i < newCount; i++) {
                fragmentIndexes.add(i);
                // 同时更新SharedPreferences
                SharedPreferencesUtil.putData("key" + i, false);
            }

            if (pagerAdapter != null) {
                pagerAdapter.setFragmentIndexes(fragmentIndexes);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to update fragments: " + e.getMessage(), e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理资源
        if (pagerAdapter != null) {
            pagerAdapter.clearFragments();
        }
    }
}
