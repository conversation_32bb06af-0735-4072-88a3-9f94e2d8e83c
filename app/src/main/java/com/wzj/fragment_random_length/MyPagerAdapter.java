package com.wzj.fragment_random_length.mvp.utils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.wzj.fragment_random_length.mvp.TypeOneFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * ViewPager适配器
 * 优化了数据更新和内存管理
 */
public class MyPagerAdapter extends FragmentStatePagerAdapter {
    private static final String TAG = "MyPagerAdapter";

    private List<Integer> fragmentIndexes;
    private List<String> fragmentTitles;

    public MyPagerAdapter(@NonNull FragmentManager fm) {
        super(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        fragmentIndexes = new ArrayList<>();
        fragmentTitles = new ArrayList<>();
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        if (position < 0 || position >= fragmentIndexes.size()) {
            throw new IndexOutOfBoundsException("Position " + position + " is out of bounds");
        }
        return TypeOneFragment.newInstance(fragmentIndexes.get(position));
    }

    @Override
    public int getCount() {
        return fragmentIndexes.size();
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        if (position < 0 || position >= getCount()) {
            return null;
        }

        // 如果有自定义标题，使用自定义标题
        if (position < fragmentTitles.size() && fragmentTitles.get(position) != null) {
            return fragmentTitles.get(position);
        }

        // 否则使用默认标题
        return "Tab " + (position + 1);
    }

    /**
     * 更新Fragment索引列表
     *
     * @param indexes 新的索引列表
     */
    public void setFragmentIndexes(List<Integer> indexes) {
        if (indexes == null) {
            this.fragmentIndexes.clear();
        } else {
            this.fragmentIndexes.clear();
            this.fragmentIndexes.addAll(indexes);
        }
        notifyDataSetChanged();
    }

    /**
     * 设置自定义标题
     *
     * @param titles 标题列表
     */
    public void setFragmentTitles(List<String> titles) {
        if (titles == null) {
            this.fragmentTitles.clear();
        } else {
            this.fragmentTitles.clear();
            this.fragmentTitles.addAll(titles);
        }
        notifyDataSetChanged();
    }

    /**
     * 添加单个Fragment
     *
     * @param index Fragment索引
     * @param title Fragment标题（可选）
     */
    public void addFragment(int index, @Nullable String title) {
        fragmentIndexes.add(index);
        if (title != null) {
            // 确保titles列表大小与indexes一致
            while (fragmentTitles.size() < fragmentIndexes.size() - 1) {
                fragmentTitles.add(null);
            }
            fragmentTitles.add(title);
        }
        notifyDataSetChanged();
    }

    /**
     * 移除指定位置的Fragment
     *
     * @param position 要移除的位置
     */
    public void removeFragment(int position) {
        if (position >= 0 && position < fragmentIndexes.size()) {
            fragmentIndexes.remove(position);
            if (position < fragmentTitles.size()) {
                fragmentTitles.remove(position);
            }
            notifyDataSetChanged();
        }
    }

    /**
     * 清空所有Fragment
     */
    public void clearFragments() {
        fragmentIndexes.clear();
        fragmentTitles.clear();
        notifyDataSetChanged();
    }
}
