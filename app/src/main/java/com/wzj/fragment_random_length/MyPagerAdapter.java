package com.wzj.fragment_random_length;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.wzj.fragment_random_length.mvp.TypeOneFragment;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class MyPagerAdapter extends FragmentStatePagerAdapter {

    private List<Integer> fragmentIndexes;

    public MyPagerAdapter(@NonNull FragmentManager fm) {
        super(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        fragmentIndexes = new ArrayList<>();
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        // 根据位置返回对应的 Fragment
//        return MyFragment.newInstance(fragmentIndexes.get(position));
        return TypeOneFragment.newInstance(fragmentIndexes.get(position));

    }

    @Override
    public int getCount() {
        // 返回要显示的 Fragment 数量
        return fragmentIndexes.size();
    }


    @Override
    public CharSequence getPageTitle(int position) {
        // 为每个 Fragment 设置标题
        return "Tab " + (position + 1);  // 例如：Tab 1, Tab 2, ...
    }

    // 更新数据源
    public void setFragmentIndexes(List<Integer> indexes) {
        this.fragmentIndexes = indexes;
        notifyDataSetChanged();  // 刷新 ViewPager
    }
}
