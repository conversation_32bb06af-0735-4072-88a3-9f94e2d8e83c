package com.wzj.fragment_random_length.constants;

/**
 * 应用常量类
 * 统一管理项目中的常量
 */
public final class AppConstants {
    
    // 防止实例化
    private AppConstants() {
        throw new AssertionError("No instances allowed");
    }
    
    /**
     * SharedPreferences相关常量
     */
    public static final class SharedPrefs {
        public static final String PREF_NAME = "data";
        public static final String KEY_PREFIX = "key";
        
        public static String getFragmentStateKey(int index) {
            return KEY_PREFIX + index;
        }
    }
    
    /**
     * Fragment相关常量
     */
    public static final class Fragment {
        public static final String ARG_INDEX = "index";
        public static final int DEFAULT_FRAGMENT_COUNT = 6;
        
        // Fragment内容模板
        public static final String CONTENT_TEMPLATE_NORMAL = "TypeOneFragment 我是第%d个Fragment,我的下标是%d！";
        public static final String CONTENT_TEMPLATE_ACTIVE = "当前是第%d个Fragment!";
        
        public static String getNormalContent(int index) {
            return String.format(CONTENT_TEMPLATE_NORMAL, index + 1, index);
        }
        
        public static String getActiveContent(int index) {
            return String.format(CONTENT_TEMPLATE_ACTIVE, index + 1);
        }
    }
    
    /**
     * UI相关常量
     */
    public static final class UI {
        public static final String TAB_TITLE_TEMPLATE = "Tab %d";
        
        public static String getTabTitle(int position) {
            return String.format(TAB_TITLE_TEMPLATE, position + 1);
        }
    }
    
    /**
     * 日志标签常量
     */
    public static final class LogTags {
        public static final String MAIN_ACTIVITY = "MainActivity";
        public static final String TYPE_ONE_FRAGMENT = "TypeOneFragment";
        public static final String TYPE_PRESENTER = "TypePresenter";
        public static final String PAGER_ADAPTER = "MyPagerAdapter";
        public static final String BASE_FRAGMENT = "BaseFragment";
        public static final String SHARED_PREFS_UTIL = "SharedPreferencesUtil";
    }
}
