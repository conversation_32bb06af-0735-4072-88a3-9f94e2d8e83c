package com.wzj.fragment_random_length.model;

/**
 * Fragment数据模型
 * 用于封装Fragment的状态和内容
 */
public class FragmentData {
    private int index;
    private String content;
    private boolean isActive;
    private String title;

    public FragmentData() {
    }

    public FragmentData(int index, String content, boolean isActive) {
        this.index = index;
        this.content = content;
        this.isActive = isActive;
        this.title = "Tab " + (index + 1);
    }

    public FragmentData(int index, String content, boolean isActive, String title) {
        this.index = index;
        this.content = content;
        this.isActive = isActive;
        this.title = title;
    }

    // Getters and Setters
    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return "FragmentData{" +
                "index=" + index +
                ", content='" + content + '\'' +
                ", isActive=" + isActive +
                ", title='" + title + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FragmentData that = (FragmentData) o;

        return index == that.index;
    }

    @Override
    public int hashCode() {
        return index;
    }
}
