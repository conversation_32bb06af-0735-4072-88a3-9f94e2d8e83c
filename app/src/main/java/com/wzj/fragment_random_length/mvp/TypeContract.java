package com.wzj.fragment_random_length.mvp;

import com.wzj.fragment_random_length.mvp.base.IPresenter;
import com.wzj.fragment_random_length.mvp.base.IView;
import java.util.ArrayList;
import java.util.List;

/**
 * TypeContract - MVP契约接口
 * 简化后的版本，移除了未使用的方法
 */
public class TypeContract {

    /**
     * View接口 - 定义UI操作
     */
    public interface ITypeView extends IView {
        /**
         * 设置内容文本
         * @param titles 内容列表
         */
        void setContentText(ArrayList<String> titles);

        /**
         * 显示进度条
         */
        void visProgressBar();

        /**
         * 隐藏进度条
         */
        void goneProgressBar();

        /**
         * 设置Tab布局（预留接口）
         * @param titles 标题列表
         */
        void setTabLayout(ArrayList<String> titles);

        /**
         * 点击事件处理（预留接口）
         */
        void click(String name, String mp4Url, String horizonalPicUrl, String picUrl, String id, String resourceFlag);
    }

    /**
     * Presenter接口 - 定义业务逻辑操作
     */
    public interface ITypePresenter extends IPresenter {
        /**
         * 设置数据（预留接口）
         * @param list 数据列表
         */
        void setData(List<String> list);

        /**
         * 根据类型获取数据
         * @param type 类型
         * @param pos 位置
         * @param isPlay 是否为播放状态
         */
        void getDataByType(String type, int pos, boolean isPlay);

        /**
         * 获取收藏数据
         */
        void getFavoritesData();
    }
}
