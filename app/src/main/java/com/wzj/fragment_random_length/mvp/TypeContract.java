package com.wzj.fragment_random_length.mvp;



import com.wzj.fragment_random_length.mvp.base.IPresenter;
import com.wzj.fragment_random_length.mvp.base.IView;

import java.util.ArrayList;
import java.util.List;

public class TypeContract {
    public interface ITypeView extends IView {
        /**
         * add tab
         *
         * @param titles
         */
        void setTabLayout(ArrayList<String> titles);

        void setContentText(ArrayList<String> titles);


        void visProgressBar();
        /**
         * 隐藏掉progressbar
         */
        void goneProgressBar();

        void click(String name, String mp4Url, String horizonalPicUrl, String picUrl, String id, String resourceFlag);

    }


    public interface ITypePresenter extends IPresenter {
        /**
         * 加载recyleview data
         */
        void setData(List<String> list);


        /**
         * 根据类型获取资源列表
         * type
         */
        void getDataByType(String type, int pos, boolean isPlay);

        /**
         * 获取收藏数据
         */
        void getFavoritesData();
    }
}
