package com.wzj.fragment_random_length.mvp;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;
import androidx.annotation.Nullable;
import com.wzj.fragment_random_length.R;
import com.wzj.fragment_random_length.mvp.base.BaseFragment;
import com.wzj.fragment_random_length.mvp.utils.SharedPreferencesUtil;
import java.util.ArrayList;

/**
 * <AUTHOR>
 */
public class TypeOneFragment extends BaseFragment<TypePresenter> implements TypeContract.ITypeView {
    private final static String TAG = "TypeOneFragment";
    private static final String ARG_INDEX = "index";
    private int index;
    private TextView textView;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate for fragment");

        // 安全地获取传递的参数
        Bundle arguments = getArguments();
        if (arguments != null) {
            index = arguments.getInt(ARG_INDEX, 0);
        } else {
            Log.w(TAG, "No arguments provided, using default index 0");
            index = 0;
        }
    }
    @Override
    public int getLayoutResId() {
        return R.layout.fragment_type_one;
    }


    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void initView() {
        try {
            // 显示该 Fragment 的内容，可以根据 index 动态改变
            textView = rootView.findViewById(R.id.textView);
            if (textView == null) {
                Log.e(TAG, "TextView not found in layout");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in initView: " + e.getMessage(), e);
        }
    }

    @Override
    public void initData() {
        switch (index){
            case 0:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第一个Fragment,我的下标是0！");
                }else {
                    textView.setText("当前是第1个Fragment!");
                }
                break;
            case 1:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第二个Fragment,我的下标是1！");
                }else {
                    textView.setText("当前是第2个Fragment!");
                }
                break;
            case 2:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第三个Fragment,我的下标是2！");
                }else {
                    textView.setText("当前是第3个Fragment!");
                }
                break;
            case 3:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第四个Fragment,我的下标是3！");
                }else {
                    textView.setText("当前是第4个Fragment!");
                }
                break;
            case 4:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第五个Fragment,我的下标是4！");
                }else {
                    textView.setText("当前是第5个Fragment!");
                }
                break;
            case 5:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第六个Fragment,我的下标是5！");
                }else {
                    textView.setText("当前是第6个Fragment!");
                }
                break;
            case 6:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第七个Fragment,我的下标是6！");
                }else {
                    textView.setText("当前是第7个Fragment!");
                }
                break;
            case 7:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第八个Fragment,我的下标是7！");
                }else {
                    textView.setText("当前是第8个Fragment!");
                }
                break;
            case 8:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第九个Fragment,我的下标是8！");
                }else {
                    textView.setText("当前是第9个Fragment!");
                }
                break;
            case 9:
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    textView.setText("TypeOneFragment 我是第十个Fragment,我的下标是9！");
                }else {
                    textView.setText("当前是第10个Fragment!");
                }
                break;
            default:
                break;
        }

        textView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(!(boolean)SharedPreferencesUtil.getData("key"+index,false)) {
                    SharedPreferencesUtil.putData("key"+index, true);
                    mPresenter.getDataByType("",index,true);
                }else {
                    SharedPreferencesUtil.putData("key"+index, false);
                    mPresenter.getDataByType("",index,false);
                }
            }
        });
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        //在fragment可见时在加载数据，以防viewpager与加载fragment造成卡顿，
        //但第一个fragment不会进入判断所以单独处理
        if (!isVisibleToUser){
        }
        //并没有被销毁时会走这里获取数据
//        isCanLoadData();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void showOrHideTitle(boolean isShow) {
        Log.e(TAG, "showOrHideTitle: ----------------->"+isShow );
    }

    public static TypeOneFragment newInstance(int index) {
        TypeOneFragment fragment = new TypeOneFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_INDEX, index);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void setTabLayout(ArrayList<String> titles) {

    }

    @Override
    public void setContentText(ArrayList<String> titles) {
        if (textView != null && titles != null && !titles.isEmpty()) {
            textView.setText(titles.get(0));
        } else {
            Log.w(TAG, "Cannot set content text: textView=" + textView + ", titles=" + titles);
        }
    }


    @Override
    public void visProgressBar() {
    }

    @Override
    public void goneProgressBar() {
    }

    @Override
    public void click(String name, String mp4Url, String horizonalPicUrl, String picUrl, String id, String resourceFlag) {

    }


}