package com.wzj.fragment_random_length.mvp;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;
import androidx.annotation.Nullable;
import com.wzj.fragment_random_length.R;
import com.wzj.fragment_random_length.constants.AppConstants;
import com.wzj.fragment_random_length.mvp.base.BaseFragment;
import com.wzj.fragment_random_length.mvp.utils.SharedPreferencesUtil;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * 优化后的版本，消除了重复代码
 */
public class TypeOneFragment extends BaseFragment<TypePresenter> implements TypeContract.ITypeView {
    private static final String TAG = AppConstants.LogTags.TYPE_ONE_FRAGMENT;
    private int index;
    private TextView textView;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate for fragment");

        // 安全地获取传递的参数
        Bundle arguments = getArguments();
        if (arguments != null) {
            index = arguments.getInt(AppConstants.Fragment.ARG_INDEX, 0);
        } else {
            Log.w(TAG, "No arguments provided, using default index 0");
            index = 0;
        }
    }
    @Override
    public int getLayoutResId() {
        return R.layout.fragment_type_one;
    }


    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void initView() {
        try {
            // 显示该 Fragment 的内容，可以根据 index 动态改变
            textView = rootView.findViewById(R.id.textView);
            if (textView == null) {
                Log.e(TAG, "TextView not found in layout");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in initView: " + e.getMessage(), e);
        }
    }

    @Override
    public void initData() {
        try {
            // 使用统一的方法设置初始内容
            updateFragmentContent();

            // 设置点击监听器
            setupClickListener();
        } catch (Exception e) {
            Log.e(TAG, "Error in initData: " + e.getMessage(), e);
        }
    }

    /**
     * 更新Fragment内容
     */
    private void updateFragmentContent() {
        if (textView == null) {
            Log.w(TAG, "TextView is null, cannot update content");
            return;
        }

        try {
            String stateKey = AppConstants.SharedPrefs.getFragmentStateKey(index);
            boolean isActive = (Boolean) SharedPreferencesUtil.getData(stateKey, false);

            String content = isActive
                ? AppConstants.Fragment.getActiveContent(index)
                : AppConstants.Fragment.getNormalContent(index);

            textView.setText(content);
            Log.d(TAG, "Content updated for index " + index + ", isActive: " + isActive);
        } catch (Exception e) {
            Log.e(TAG, "Error updating fragment content: " + e.getMessage(), e);
            // 设置默认内容
            textView.setText("Fragment " + (index + 1));
        }
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListener() {
        if (textView == null) {
            Log.w(TAG, "TextView is null, cannot setup click listener");
            return;
        }

        textView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    String stateKey = AppConstants.SharedPrefs.getFragmentStateKey(index);
                    boolean currentState = (Boolean) SharedPreferencesUtil.getData(stateKey, false);
                    boolean newState = !currentState;

                    SharedPreferencesUtil.putData(stateKey, newState);

                    if (mPresenter != null) {
                        mPresenter.getDataByType("", index, newState);
                    }

                    Log.d(TAG, "State changed for index " + index + ": " + currentState + " -> " + newState);
                } catch (Exception e) {
                    Log.e(TAG, "Error handling click: " + e.getMessage(), e);
                }
            }
        });
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        //在fragment可见时在加载数据，以防viewpager与加载fragment造成卡顿，
        //但第一个fragment不会进入判断所以单独处理
        if (!isVisibleToUser){
        }
        //并没有被销毁时会走这里获取数据
//        isCanLoadData();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }


    public static TypeOneFragment newInstance(int index) {
        TypeOneFragment fragment = new TypeOneFragment();
        Bundle args = new Bundle();
        args.putInt(AppConstants.Fragment.ARG_INDEX, index);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void setTabLayout(ArrayList<String> titles) {

    }

    @Override
    public void setContentText(ArrayList<String> titles) {
        if (textView != null && titles != null && !titles.isEmpty()) {
            textView.setText(titles.get(0));
        } else {
            Log.w(TAG, "Cannot set content text: textView=" + textView + ", titles=" + titles);
        }
    }


    @Override
    public void visProgressBar() {
    }

    @Override
    public void goneProgressBar() {
    }

    @Override
    public void click(String name, String mp4Url, String horizonalPicUrl, String picUrl, String id, String resourceFlag) {

    }


}