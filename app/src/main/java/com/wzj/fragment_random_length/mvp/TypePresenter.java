package com.wzj.fragment_random_length.mvp;

import android.util.Log;

import com.wzj.fragment_random_length.constants.AppConstants;
import com.wzj.fragment_random_length.mvp.base.BasePresenter;

import java.util.ArrayList;
import java.util.List;

/**
 * TypePresenter - 处理Fragment的业务逻辑
 * 优化后的版本，消除了重复代码和硬编码
 */
public class TypePresenter extends BasePresenter<TypeContract.ITypeView> implements TypeContract.ITypePresenter {
    private static final String TAG = AppConstants.LogTags.TYPE_PRESENTER;
    private final ArrayList<String> contentList = new ArrayList<>();

    @Override
    public void onResume() {
        // 可以在这里处理恢复逻辑
        Log.d(TAG, "onResume");
    }

    @Override
    public void onStop() {
        // 可以在这里处理暂停逻辑
        Log.d(TAG, "onStop");
    }

    @Override
    public void destroy() {
        if (isViewAttached()) {
            getView().goneProgressBar();
        }
        super.destroy();
    }

    @Override
    public void setData(List<String> list) {
        // 预留方法，可用于设置其他数据
        Log.d(TAG, "setData called with " + (list != null ? list.size() : 0) + " items");
    }

    @Override
    public void getDataByType(String type, int pos, boolean isPlay) {
        if (!isViewAttached()) {
            Log.w(TAG, "View not attached, cannot set content");
            return;
        }

        try {
            contentList.clear();
            String content = generateContentForPosition(pos, isPlay);
            contentList.add(content);
            getView().setContentText(contentList);
            Log.d(TAG, "Content set for position " + pos + ", isPlay: " + isPlay);
        } catch (Exception e) {
            Log.e(TAG, "Error getting data by type: " + e.getMessage(), e);
        }
    }

    /**
     * 根据位置和状态生成内容
     *
     * @param position 位置索引
     * @param isPlay   是否为播放状态
     * @return 生成的内容文本
     */
    private String generateContentForPosition(int position, boolean isPlay) {
        if (position < 0) {
            Log.w(TAG, "Invalid position: " + position);
            return "无效的Fragment位置";
        }

        return isPlay
            ? AppConstants.Fragment.getActiveContent(position)
            : AppConstants.Fragment.getNormalContent(position);
    }

    @Override
    public void getFavoritesData() {
        if (!isViewAttached()) {
            Log.w(TAG, "View not attached, cannot load favorites");
            return;
        }

        try {
            // 这里可以实现收藏数据的加载逻辑
            Log.d(TAG, "Loading favorites data");
            getView().visProgressBar();

            // 模拟异步加载
            // 实际项目中这里应该是网络请求或数据库查询
            loadFavoritesFromCache();
        } catch (Exception e) {
            Log.e(TAG, "Error loading favorites: " + e.getMessage(), e);
            if (isViewAttached()) {
                getView().goneProgressBar();
            }
        }
    }

    /**
     * 从缓存加载收藏数据
     */
    private void loadFavoritesFromCache() {
        // 模拟缓存数据加载
        contentList.clear();
        contentList.add("收藏的内容加载中...");

        if (isViewAttached()) {
            getView().setContentText(contentList);
            getView().goneProgressBar();
        }
    }
}
