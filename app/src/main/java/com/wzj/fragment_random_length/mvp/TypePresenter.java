package com.wzj.fragment_random_length.mvp;

import android.content.ContentResolver;
import android.content.res.Resources;
import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;


import com.wzj.fragment_random_length.mvp.base.BasePresenter;
import com.wzj.fragment_random_length.mvp.base.IView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class TypePresenter extends BasePresenter<TypeContract.ITypeView> implements TypeContract.ITypePresenter {
    private static final String TAG = "TypePresenter";
    private ArrayList<String> list = new ArrayList<>();

    // 配置化的Fragment内容
    private static final String[] FRAGMENT_CONTENTS = {
        "TypeOneFragment 我是第一个Fragment,我的下标是0！",
        "TypeOneFragment 我是第二个Fragment,我的下标是1！",
        "TypeOneFragment 我是第三个Fragment,我的下标是2！",
        "TypeOneFragment 我是第四个Fragment,我的下标是3！",
        "TypeOneFragment 我是第五个Fragment,我的下标是4！",
        "TypeOneFragment 我是第六个Fragment,我的下标是5！"
    };

    private static final String[] FRAGMENT_PLAY_CONTENTS = {
        "当前是第1个Fragment!",
        "当前是第2个Fragment!",
        "当前是第3个Fragment!",
        "当前是第4个Fragment!",
        "当前是第5个Fragment!",
        "当前是第6个Fragment!"
    };

    @Override
    public void onResume() {
        // 可以在这里处理恢复逻辑
    }

    @Override
    public void onStop() {
        // 可以在这里处理暂停逻辑
    }

    @Override
    public void destroy() {
        if (isViewAttached()) {
            getView().goneProgressBar();
        }
        super.destroy();
    }

    @Override
    public void setData(List<String> list) {
    }

    @Override
    public void getDataByType(String type, int pos, boolean isPlay) {
        if (!isViewAttached()) {
            Log.w(TAG, "View not attached, cannot set content");
            return;
        }

        list.clear();

        try {
            String content = getContentForPosition(pos, isPlay);
            if (content != null) {
                list.add(content);
                getView().setContentText(list);
            } else {
                Log.w(TAG, "No content available for position: " + pos);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting data by type: " + e.getMessage(), e);
        }
    }

    /**
     * 根据位置和播放状态获取内容
     *
     * @param pos    位置索引
     * @param isPlay 是否播放状态
     * @return 对应的内容文本
     */
    private String getContentForPosition(int pos, boolean isPlay) {
        if (pos < 0) {
            return null;
        }

        String[] contentArray = isPlay ? FRAGMENT_PLAY_CONTENTS : FRAGMENT_CONTENTS;

        if (pos < contentArray.length) {
            return contentArray[pos];
        } else {
            // 对于超出预定义范围的位置，动态生成内容
            if (isPlay) {
                return "当前是第" + (pos + 1) + "个Fragment!";
            } else {
                return "TypeOneFragment 我是第" + (pos + 1) + "个Fragment,我的下标是" + pos + "！";
            }
        }
    }

    /**
     * 无网时候获取缓存数据
     * @return
     */
    public void getCacheAtmosphere() {
    }


    @Override
    public void getFavoritesData() {
        //tab position = 1 时加载收藏数据

    }
}
