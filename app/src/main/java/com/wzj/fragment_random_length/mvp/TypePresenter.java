package com.wzj.fragment_random_length.mvp;

import android.content.ContentResolver;
import android.content.res.Resources;
import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;


import com.wzj.fragment_random_length.mvp.base.BasePresenter;
import com.wzj.fragment_random_length.mvp.base.IView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class TypePresenter extends BasePresenter<TypeContract.ITypeView> implements TypeContract.ITypePresenter {
    private static final String TAG = "TypePresenter";
    private TypeContract.ITypeView mView;
    private ArrayList<String> list = new ArrayList<>();



    @Override
    public void init(IView v) {
        mView = (TypeContract.ITypeView) v;
    }

    @Override
    public void onResume() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void destroy() {
        mView.goneProgressBar();
    }

    @Override
    public void setData(List<String> list) {
    }

    @Override
    public void getDataByType(String type, int pos, boolean isPaly) {
        list.clear();
        switch (pos){
            case 0:
                if(Boolean.FALSE.equals(isPaly)) {
                    list.add("TypeOneFragment 我是第一个Fragment,我的下标是0！");
                }else {
                    list.add("当前是第1个Fragment!");
                }
                break;
            case 1:
                if(Boolean.FALSE.equals(isPaly)) {
                    list.add("TypeOneFragment 我是第二个Fragment,我的下标是1！");
                }else {
                    list.add("当前是第2个Fragment!");
                }
                break;
            case 2:
                if(Boolean.FALSE.equals(isPaly)) {
                    list.add("TypeOneFragment 我是第三个Fragment,我的下标是2！");
                }else {
                    list.add("当前是第3个Fragment!");
                }
                break;
            case 3:
                if(Boolean.FALSE.equals(isPaly)) {
                    list.add("TypeOneFragment 我是第四个Fragment,我的下标是3！");
                }else {
                    list.add("当前是第4个Fragment!");
                }
                break;
            case 4:
                if(Boolean.FALSE.equals(isPaly)) {
                    list.add("TypeOneFragment 我是第五个Fragment,我的下标是4！");
                }else {
                    list.add("当前是第5个Fragment!");
                }
                break;
            case 5:
                if(Boolean.FALSE.equals(isPaly)) {
                    list.add("TypeOneFragment 我是第六个Fragment,我的下标是5！");
                }else {
                    list.add("当前是第6个Fragment!");
                }
                break;
            default:
                break;
        }
        mView.setContentText(list);


    }

    /**
     * 无网时候获取缓存数据
     * @return
     */
    public void getCacheAtmosphere() {
    }


    @Override
    public void getFavoritesData() {
        //tab position = 1 时加载收藏数据

    }
}
