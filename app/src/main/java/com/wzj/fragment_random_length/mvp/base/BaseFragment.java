package com.wzj.fragment_random_length.mvp.base;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.wzj.fragment_random_length.mvp.utils.getTUtils;


public abstract class BaseFragment<P extends IPresenter> extends Fragment implements IView {
    protected static final String TAG = "BaseFragment";
    protected P mPresenter;
    protected View rootView;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        Log.e("BaseFragment","onCreate");
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.e("BaseFragment","onCreateView");
        rootView = inflater.inflate(getLayoutResId(), container, false);
        return rootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        try {
            mPresenter = getTUtils.getT(this, 0);
            if (mPresenter != null) {
                mPresenter.init(this);
                initView();
                initData();
            } else {
                Log.e(TAG, "Failed to create presenter instance");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onViewCreated: " + e.getMessage(), e);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mPresenter != null) {
            mPresenter.onResume();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mPresenter != null) {
            mPresenter.onStop();
        }
    }

    /**
     * 获取布局的ID
     *
     * @return
     */
    public abstract int getLayoutResId();

    /**
     * 初始化控件
     *
     * @return
     */
    public abstract void initView();

    /**
     * 初始化数据
     *
     * @return
     */
    public abstract void initData();

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mPresenter != null) {
            mPresenter.destroy();
            mPresenter = null;
        }
    }

    public abstract void showOrHideTitle(boolean isShow);
}
