package com.wzj.fragment_random_length.mvp.base;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR> 为之后的Presenter提供的一个基类
 * 使用弱引用防止内存泄漏，实现IPresenter接口
 */
public abstract class BasePresenter<V extends IView> implements IPresenter {

    private WeakReference<V> viewRef;

    @Override
    public void init(IView view) {
        attachView((V) view);
    }

    public void attachView(V view) {
        if (view != null) {
            this.viewRef = new WeakReference<>(view);
        }
    }

    public void detachView() {
        if (viewRef != null) {
            viewRef.clear();
            viewRef = null;
        }
    }

    public V getView() {
        return viewRef != null ? viewRef.get() : null;
    }

    protected boolean isViewAttached() {
        return getView() != null;
    }

    @Override
    public void onResume() {
        // 子类可以重写此方法
    }

    @Override
    public void onStop() {
        // 子类可以重写此方法
    }

    @Override
    public void destroy() {
        detachView();
    }
}
