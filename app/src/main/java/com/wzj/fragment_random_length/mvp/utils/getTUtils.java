package com.wzj.fragment_random_length.mvp.utils;

import android.util.Log;

import java.lang.reflect.Constructor;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR> 获取泛型实例工具类
 * 优化了异常处理和安全性检查
 */
public class getTUtils {
    private static final String TAG = "getTUtils";

    /**
     * 获取泛型实例
     *
     * @param obj   目标对象
     * @param index 泛型参数索引
     * @param <T>   泛型类型
     * @return 泛型实例，失败时返回null
     */
    @SuppressWarnings("unchecked")
    public static <T> T getT(Object obj, int index) {
        if (obj == null) {
            Log.e(TAG, "Object is null");
            return null;
        }

        try {
            Type genericSuperclass = obj.getClass().getGenericSuperclass();
            if (!(genericSuperclass instanceof ParameterizedType)) {
                Log.e(TAG, "Generic superclass is not ParameterizedType");
                return null;
            }

            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

            if (index < 0 || index >= actualTypeArguments.length) {
                Log.e(TAG, "Index out of bounds: " + index + ", available: " + actualTypeArguments.length);
                return null;
            }

            Class<T> clazz = (Class<T>) actualTypeArguments[index];

            // 尝试获取无参构造函数
            Constructor<T> constructor = clazz.getDeclaredConstructor();
            constructor.setAccessible(true);
            return constructor.newInstance();

        } catch (Exception e) {
            Log.e(TAG, "Failed to create instance for index " + index + ": " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取泛型类型的Class对象
     *
     * @param obj   目标对象
     * @param index 泛型参数索引
     * @return Class对象，失败时返回null
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getTClass(Object obj, int index) {
        if (obj == null) {
            Log.e(TAG, "Object is null");
            return null;
        }

        try {
            Type genericSuperclass = obj.getClass().getGenericSuperclass();
            if (!(genericSuperclass instanceof ParameterizedType)) {
                return null;
            }

            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

            if (index < 0 || index >= actualTypeArguments.length) {
                return null;
            }

            return (Class<T>) actualTypeArguments[index];
        } catch (Exception e) {
            Log.e(TAG, "Failed to get class for index " + index + ": " + e.getMessage(), e);
            return null;
        }
    }
}
