package com.wzj.fragment_random_length.mvp.utils;

import android.util.Log;

import java.lang.reflect.ParameterizedType;

/**
 * <AUTHOR> 获取泛型实例
 */
public class getTUtils {

    /**
     * 获取泛型实例
     *
     * @param obj
     * @param index 泛型下标
     * @param <T>
     * @return
     */
    public static  <T> T getT(Object obj, int index) {
        T t = null;
        try {
            ParameterizedType type = (ParameterizedType) obj.getClass().getGenericSuperclass();
            t = ((Class<T>) type.getActualTypeArguments()[index]).newInstance();
        } catch (Exception e) {
            Log.e("BaseActivity", "getT Exception=" + e.getMessage());
        }
        return t;
    }

}
