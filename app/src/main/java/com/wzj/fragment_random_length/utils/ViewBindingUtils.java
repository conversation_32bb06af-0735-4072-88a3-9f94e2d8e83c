package com.wzj.fragment_random_length.utils;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.viewbinding.ViewBinding;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * ViewBinding工具类
 * 用于简化ViewBinding的使用
 */
public class ViewBindingUtils {

    /**
     * 通过反射创建ViewBinding实例
     * @param clazz ViewBinding类
     * @param inflater LayoutInflater
     * @param container ViewGroup容器
     * @param <T> ViewBinding类型
     * @return ViewBinding实例
     */
    @SuppressWarnings("unchecked")
    public static <T extends ViewBinding> T inflate(Class<T> clazz, LayoutInflater inflater, ViewGroup container) {
        try {
            Method inflateMethod = clazz.getMethod("inflate", LayoutInflater.class, ViewGroup.class, boolean.class);
            return (T) inflateMethod.invoke(null, inflater, container, false);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inflate ViewBinding", e);
        }
    }

    /**
     * 通过反射创建ViewBinding实例（不带容器）
     * @param clazz ViewBinding类
     * @param inflater LayoutInflater
     * @param <T> ViewBinding类型
     * @return ViewBinding实例
     */
    @SuppressWarnings("unchecked")
    public static <T extends ViewBinding> T inflate(Class<T> clazz, LayoutInflater inflater) {
        try {
            Method inflateMethod = clazz.getMethod("inflate", LayoutInflater.class);
            return (T) inflateMethod.invoke(null, inflater);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inflate ViewBinding", e);
        }
    }

    /**
     * 从泛型参数中获取ViewBinding类型
     * @param obj 目标对象
     * @param index 泛型参数索引
     * @param <T> ViewBinding类型
     * @return ViewBinding类型的Class对象
     */
    @SuppressWarnings("unchecked")
    public static <T extends ViewBinding> Class<T> getViewBindingClass(Object obj, int index) {
        Type genericSuperclass = obj.getClass().getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (index < actualTypeArguments.length) {
                return (Class<T>) actualTypeArguments[index];
            }
        }
        throw new IllegalArgumentException("Cannot find ViewBinding class at index " + index);
    }
}
